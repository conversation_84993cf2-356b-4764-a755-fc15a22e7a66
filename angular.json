{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"vertical-iconsidebar": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["dragula", "chart.js", "@ks89/angular-modal-gallery", "angular2-chartjs", "apexcharts", "ng-apexcharts"], "outputPath": "dist/vertical-iconsidebar", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/.htaccess"], "styles": ["src/styles.scss", "src/assets/css/loader.css", "src/assets/switcher/demo.css", "src/assets/switcher/css/switcher.css", "./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "./node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "./node_modules/@ng-select/ng-select/themes/default.theme.css", "./node_modules/ngx-ui-switch/ui-switch.component.css"], "scripts": ["node_modules/cookieconsent/build/cookieconsent.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "4mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"browserTarget": "vertical-iconsidebar:build:production"}, "development": {"browserTarget": "vertical-iconsidebar:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "vertical-iconsidebar:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}}}}, "defaultProject": "vertical-iconsidebar"}