// Variables de couleurs professionnelles Affilink
$primary-color: #664DC9;        // Primaire (violet foncé)
$secondary-color: #A8A4F2;      // Secondaire lavande
$text-dark: #2F2F2F;            // Texte foncé
$text-light: #828282;           // Texte clair
$bg-alt: #F4F3FC;               // Fond alternatif
$border-color: #DCDCE6;         // Bordures
$success-color: #10B981;        // Succès
$error-color: #EF4444;          // Erreur

// Anciennes variables pour compatibilité
$accent-color: $secondary-color;
$warning-color: #f59e0b;
$danger-color: $error-color;
$light-bg: $bg-alt;
$card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

// Dashboard Header moderne
.dashboard-header {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border-radius: 16px;
  padding: 1rem 2rem; // Réduction du padding vertical
  margin-bottom: 1.5rem;
  color: white;
  box-shadow: 0 8px 32px rgba($primary-color, 0.3);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
  }

  .welcome-section {
    flex: 1;
    min-width: 300px;

    .welcome-title {
      font-size: 2.25rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: white;
      line-height: 1.2;
    }

    .welcome-subtitle {
      font-size: 1.125rem;
      margin: 0;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 400;
    }
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
  }

  .period-selector {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .period-label {
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      font-size: 0.875rem;
    }

    .period-toggle-group {
      background: rgba(255, 255, 255, 0.15);
      border-radius: 8px;
      backdrop-filter: blur(10px);

      .period-toggle {
        color: rgba(255, 255, 255, 0.9);
        border: none;
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.5rem 1rem;

        &.mat-button-toggle-checked {
          background: rgba(255, 255, 255, 0.25);
          color: white;
          font-weight: 600;
        }

        &:hover:not(.mat-button-toggle-checked) {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  .refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    font-weight: 500;

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    &:disabled {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.6);
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Section Titles
.section-title {
  color: #374151;
  font-weight: 600;
  font-size: 1.25rem; // Réduction de la taille (1.5rem -> 1.25rem)
  margin-bottom: 1rem; // Réduction de l'espacement
  display: flex;
  align-items: center;

  mat-icon {
    color: $primary-color;
    font-size: 1.25rem; // Réduction de l'icône aussi
    margin-right: 0.5rem;
  }
}

// KPI Section moderne
.kpi-section {
  .kpi-grid {
    display: flex;
    flex-wrap: nowrap;
    gap: 1rem;
    margin-top: 1.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;

    // Scroll horizontal discret
    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: $primary-color;
      border-radius: 2px;
    }
  }

  .kpi-card-wrapper {
    position: relative;
    min-width: 200px; // Largeur minimale pour chaque carte
    flex: 1; // Répartition équitable de l'espace
  }

  .modern-card {
    background: white;
    border-radius: 16px;
    padding: 1rem; // Réduction du padding
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid $border-color;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 120px; // Hauteur fixe plus compacte

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .kpi-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem; // Réduction de l'espacement

    .kpi-icon-wrapper {
      width: 36px; // Réduction de la taille
      height: 36px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.75rem;
      transition: transform 0.3s ease;

      .kpi-icon {
        font-size: 18px; // Réduction de la taille de l'icône
        width: 18px;
        height: 18px;
      }
    }

    .kpi-title {
      font-size: 0.75rem; // Réduction de la taille du texte
      font-weight: 600;
      color: $text-dark;
      margin: 0;
      line-height: 1.4;
    }
  }

  .kpi-value-section {
    margin-bottom: 0.5rem; // Réduction de l'espacement

    .kpi-value {
      font-size: 1.5rem; // Réduction de la taille de la valeur
      font-weight: 700;
      margin: 0;
      line-height: 1.2;
      color: $text-dark;
    }
  }

  .kpi-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;

    .kpi-variation {
      display: flex;
      align-items: center;
      font-weight: 600;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;

      .variation-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
        margin-right: 0.25rem;
      }

      .variation-value {
        font-weight: 600;
      }

      &.text-success {
        background: rgba($success-color, 0.1);
        color: $success-color;
      }

      &.text-danger {
        background: rgba($error-color, 0.1);
        color: $error-color;
      }
    }

    .variation-label,
    .no-comparison {
      color: $text-light;
      font-weight: 500;
    }
  }

  .kpi-progress {
    margin-top: 0.75rem;
    height: 4px;
    background: $bg-alt;
    border-radius: 2px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      border-radius: 2px;
      transition: width 0.8s ease;
    }
  }

  // Couleurs spécifiques par type de carte
  .kpi-primary {
    .kpi-icon-wrapper {
      background: rgba($primary-color, 0.1);
      .kpi-icon { color: $primary-color; }
    }
    .kpi-value { color: $primary-color; }
  }

  .kpi-success {
    .kpi-icon-wrapper {
      background: rgba($success-color, 0.1);
      .kpi-icon { color: $success-color; }
    }
    .kpi-value { color: $success-color; }
  }

  .kpi-warning {
    .kpi-icon-wrapper {
      background: rgba(#f59e0b, 0.1);
      .kpi-icon { color: #f59e0b; }
    }
    .kpi-value { color: #f59e0b; }
  }

  .kpi-accent {
    .kpi-icon-wrapper {
      background: rgba($secondary-color, 0.1);
      .kpi-icon { color: $secondary-color; }
    }
    .kpi-value { color: $secondary-color; }
  }

  .kpi-info {
    .kpi-icon-wrapper {
      background: rgba(#3b82f6, 0.1);
      .kpi-icon { color: #3b82f6; }
    }
    .kpi-value { color: #3b82f6; }
  }

  .kpi-secondary {
    .kpi-icon-wrapper {
      background: rgba($text-light, 0.1);
      .kpi-icon { color: $text-light; }
    }
    .kpi-value { color: $text-light; }
  }
}

// Skeleton Loader moderne
.skeleton-card {
  .skeleton-loader {
    .skeleton-header {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;

      .skeleton-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        margin-right: 0.75rem;
      }

      .skeleton-title {
        flex: 1;
        height: 1rem;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
      }
    }

    .skeleton-value {
      height: 2rem;
      width: 70%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 1rem;
    }

    .skeleton-footer {
      .skeleton-variation {
        height: 0.75rem;
        width: 50%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
      }
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Charts Section moderne avec ApexCharts
.charts-section {
  .charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr; // Évolution à gauche (plus large), donut à droite
    gap: 1rem; // Réduction de l'espacement
    margin-top: 1rem;

    .chart-card-wrapper {
      position: relative;

      &.full-width {
        grid-column: 1 / -1;
      }
    }

    // Layout spécifique pour le nouveau design
    .evolution-chart {
      grid-row: 1 / 3; // Prend toute la hauteur à gauche
    }

    .right-charts-column {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .donut-chart,
      .bar-chart {
        flex: 1;
      }
    }

    .modern-chart {
      background: white;
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid $border-color;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, $primary-color, $secondary-color);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }
    }

    .chart-header {
      margin-bottom: 1rem; // Réduction de l'espacement

      .chart-title {
        font-size: 1rem; // Réduction de la taille (1.125rem -> 1rem)
        font-weight: 700;
        color: $text-dark;
        margin: 0 0 0.25rem 0; // Réduction de l'espacement
        line-height: 1.4;
      }

      .chart-subtitle {
        font-size: 0.75rem; // Réduction de la taille (0.875rem -> 0.75rem)
        color: $text-light;
        margin: 0;
        font-weight: 500;
      }
    }

    .chart-content {
      position: relative;
      min-height: 220px; // Réduction de 40% (350px -> 220px)

      // Styles pour ApexCharts
      ::ng-deep {
        .apexcharts-canvas {
          margin: 0 auto;
        }

        .apexcharts-legend {
          justify-content: center !important;
        }

        .apexcharts-tooltip {
          background: rgba(255, 255, 255, 0.95) !important;
          border: 1px solid $border-color !important;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
          border-radius: 8px !important;
        }

        .apexcharts-tooltip-title {
          background: $light-bg !important;
          border-bottom: 1px solid $border-color !important;
          font-weight: 600 !important;
          color: $text-dark !important;
        }

        .apexcharts-tooltip-series-group {
          background: transparent !important;
        }

        .apexcharts-tooltip-text-y-label,
        .apexcharts-tooltip-text-y-value {
          color: $text-dark !important;
          font-weight: 500 !important;
        }
      }
    }

    // Skeleton loader pour les graphiques
    .skeleton-chart {
      .chart-skeleton {
        .skeleton-header {
          margin-bottom: 1.5rem;

          .skeleton-title {
            height: 1.125rem;
            width: 60%;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.5rem;
          }

          .skeleton-subtitle {
            height: 0.875rem;
            width: 40%;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
          }
        }

        .skeleton-chart-area {
          height: 300px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: $text-light;

          p {
            margin-top: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
          }
        }
      }
    }
  }

  // Responsive pour les graphiques
  @media (max-width: 768px) {
    .charts-grid {
      grid-template-columns: 1fr;
      gap: 1rem;

      .chart-card-wrapper.full-width {
        grid-column: 1;
      }

      .modern-chart {
        padding: 1rem;

        .chart-content {
          min-height: 300px;
        }
      }
    }
  }
}

// Tables Section
.tables-section {
  .table-card {
    border-radius: 12px;
    box-shadow: $card-shadow;
    
    mat-card-header {
      background-color: $light-bg;
      border-radius: 12px 12px 0 0;
      
      mat-card-title {
        color: #374151;
        font-weight: 600;
        display: flex;
        align-items: center;
      }
      
      mat-card-subtitle {
        color: #6b7280;
      }
    }
    
    .table-responsive {
      max-height: 300px; // Réduction de la hauteur (400px -> 300px)
      overflow-y: auto;
    }
    
    .table {
      margin-bottom: 0;
      
      th {
        background-color: $light-bg;
        border-top: none;
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
        padding: 1rem 0.75rem;
      }
      
      td {
        padding: 0.75rem;
        vertical-align: middle;
        border-color: #e5e7eb;
      }
      
      tbody tr:hover {
        background-color: rgba($primary-color, 0.05);
      }
    }
    
    .product-thumbnail {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
    }
    
    .product-name {
      font-weight: 500;
      color: #374151;
      font-size: 0.875rem;
      line-height: 1.2;
    }
    
    .empty-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #d1d5db;
      margin-bottom: 1rem;
    }
  }
}

// Top Products
.top-product-item {
  padding: 1rem 0;
  border-bottom: 1px solid #e5e7eb;
  
  &:last-child {
    border-bottom: none;
  }
  
  .rank-badge {
    width: 2rem;
    height: 2rem;
    background-color: $primary-color;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    margin-right: 1rem;
  }
  
  .product-name {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }
  
  .product-stats {
    margin-bottom: 0.5rem;
  }
  
  .product-badges {
    .badge {
      font-size: 0.75rem;
      margin-right: 0.25rem;
    }
  }
}

// Badges
.badge {
  &.bg-primary {
    background-color: $primary-color !important;
  }
  
  &.bg-success {
    background-color: $success-color !important;
  }
  
  &.bg-warning {
    background-color: $warning-color !important;
  }
  
  &.bg-danger {
    background-color: $danger-color !important;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1.5rem;

    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .welcome-section {
      text-align: center;
      min-width: auto;

      .welcome-title {
        font-size: 1.75rem;
      }

      .welcome-subtitle {
        font-size: 1rem;
      }
    }

    .header-controls {
      justify-content: center;
      gap: 1rem;
    }

    .period-selector {
      flex-direction: column;
      gap: 0.5rem;
      align-items: center;

      .period-toggle-group {
        .period-toggle {
          font-size: 0.75rem;
          padding: 0.4rem 0.8rem;
        }
      }
    }
  }

  .kpi-section {
    .kpi-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }

    .modern-card {
      padding: 1.25rem;

      .kpi-header {
        .kpi-icon-wrapper {
          width: 40px;
          height: 40px;
          margin-right: 0.5rem;

          .kpi-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }

        .kpi-title {
          font-size: 0.8rem;
        }
      }

      .kpi-value-section {
        .kpi-value {
          font-size: 1.75rem;
        }
      }
    }
  }

  .section-title {
    font-size: 1.25rem;
  }

  .chart-container {
    height: 250px !important;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    .period-toggle-group {
      .period-toggle {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
      }
    }

    .refresh-btn {
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
    }
  }
}
