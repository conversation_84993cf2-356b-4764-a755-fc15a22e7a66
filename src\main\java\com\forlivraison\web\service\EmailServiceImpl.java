package com.forlivraison.web.service;

import com.forlivraison.web.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * Implémentation du service email 
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailServiceImpl implements EmailService {
    
    private final JavaMailSender mailSender;
    private final EmailTemplateService templateService;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Value("${notifications.email.enabled:true}")
    private boolean emailNotificationsEnabled;
    
    @Override
    @Async
    public void sendMarketerWelcomeEmail(User marketer, String temporaryPassword) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email de bienvenue non envoyé pour {}", marketer.getEmail());
            return;
        }
        
        try {
            log.info("Envoi de l'email de bienvenue pour le marketer: {}", marketer.getEmail());
            
            String subject = "Bienvenue chez AFFILINK !";
            String htmlContent = templateService.generateWelcomeEmail(marketer);
            
            sendEmailWithRetry(marketer.getEmail(), subject, htmlContent, 3);
            
            log.info("✅ Email de bienvenue envoyé avec succès à: {}", marketer.getEmail());
            
        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email de bienvenue pour {}: {}", 
                     marketer.getEmail(), e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email de bienvenue: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Async
    public void sendTestEmail(String to) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email de test non envoyé");
            return;
        }
        
        try {
            log.info("Envoi de l'email de test vers: {}", to);
            
            String subject = "Test AFFILINK - Configuration Email";
            String htmlContent = templateService.generateTestEmail(to);
            
            sendEmailWithRetry(to, subject, htmlContent, 1);
            
            log.info("✅ Email de test envoyé avec succès à: {}", to);
            
        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email de test vers {}: {}", to, e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email de test: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Async
    public void sendEmail(String to, String subject, String content) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email non envoyé vers: {}", to);
            return;
        }
        
        try {
            log.info("📧 Envoi d'email vers: {} - Sujet: {}", to, subject);
            
            sendEmailWithRetry(to, subject, content, 2);
            
            log.info("✅ Email envoyé avec succès à: {}", to);
            
        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email vers {}: {}", to, e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Async
    public void sendOrderValidationEmail(User marketer, Long orderId, String orderDetails) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email de validation non envoyé");
            return;
        }
        
        try {
            log.info("📦 Envoi de l'email de validation de commande #{} pour: {}", orderId, marketer.getEmail());
            
            String subject = String.format("Commande #%d validée - AFFILINK", orderId);
            String content = generateOrderValidationContent(marketer, orderId, orderDetails);
            
            sendEmailWithRetry(marketer.getEmail(), subject, content, 2);
            
            log.info("✅ Email de validation de commande envoyé avec succès à: {}", marketer.getEmail());
            
        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email de validation pour la commande #{}: {}", 
                     orderId, e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email de validation: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Async
    public void sendOrderRejectionEmail(User marketer, Long orderId) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email de rejet non envoyé");
            return;
        }

        try {
            log.info("❌ Envoi de l'email de rejet de commande #{} pour: {}", orderId, marketer.getEmail());

            String subject = String.format("Commande #%d rejetée - AFFILINK", orderId);
            String content = generateOrderRejectionContent(marketer, orderId);

            sendEmailWithRetry(marketer.getEmail(), subject, content, 2);

            log.info("✅ Email de rejet de commande envoyé avec succès à: {}", marketer.getEmail());

        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email de rejet pour la commande #{}: {}",
                     orderId, e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email de rejet: " + e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void sendAdminSupervisorWelcomeEmail(User user, String password, String role) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email de bienvenue {} non envoyé", role);
            return;
        }

        try {
            log.info("Envoi de l'email de bienvenue {} pour: {}", role, user.getEmail());

            String subject = String.format("Bienvenue sur AFFILINK - Accès %s", role);
            String htmlContent = templateService.generateAdminSupervisorWelcomeEmail(
                user.getFirstName(), user.getLastName(), user.getEmail(), password, role);

            sendEmailWithRetry(user.getEmail(), subject, htmlContent, 3);

            log.info("✅ Email de bienvenue {} envoyé avec succès à: {}", role, user.getEmail());

        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email de bienvenue {} pour {}: {}",
                     role, user.getEmail(), e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email de bienvenue: " + e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void sendMarketerAccountValidationEmail(User marketer) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email de validation de compte non envoyé");
            return;
        }

        try {
            log.info("✅ Envoi de l'email de validation de compte pour: {}", marketer.getEmail());

            String subject = "Votre compte AFFILINK a été validé !";
            String htmlContent = templateService.generateMarketerAccountValidationEmail(
                marketer.getFirstName(), marketer.getLastName(), marketer.getEmail());

            sendEmailWithRetry(marketer.getEmail(), subject, htmlContent, 3);

            log.info("✅ Email de validation de compte envoyé avec succès à: {}", marketer.getEmail());

        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email de validation de compte pour {}: {}",
                     marketer.getEmail(), e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email de validation: " + e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void sendInvoiceGeneratedNotification(User marketer, String invoiceCode, String invoiceAmount,
                                               String invoiceDate, String pdfPath) throws Exception {
        if (!emailNotificationsEnabled) {
            log.info("Notifications email désactivées - Email de facture non envoyé");
            return;
        }

        try {
            log.info("Envoi de l'email de notification de facture {} pour: {}", invoiceCode, marketer.getEmail());

            String subject = String.format("Facture %s générée - AFFILINK", invoiceCode);
            String htmlContent = templateService.generateInvoiceNotificationEmail(
                marketer.getFirstName(),
                marketer.getLastName(),
                invoiceCode,
                invoiceAmount,
                invoiceDate,
                pdfPath
            );

            sendEmailWithRetry(marketer.getEmail(), subject, htmlContent, 3);

            log.info("✅ Email de notification de facture envoyé avec succès à: {}", marketer.getEmail());

        } catch (Exception e) {
            log.error("❌ Erreur lors de l'envoi de l'email de notification de facture pour {}: {}",
                     marketer.getEmail(), e.getMessage(), e);
            throw new Exception("Impossible d'envoyer l'email de notification de facture: " + e.getMessage(), e);
        }
    }

    /**
     * Envoie un email avec mécanisme de retry
     */
    private void sendEmailWithRetry(String to, String subject, String content, int maxRetries) throws Exception {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("Tentative {}/{} d'envoi vers: {} - Sujet: {}", attempt, maxRetries, to, subject);
                log.debug("Configuration SMTP - Host: smtp.gmail.com, Port: 587, From: {}", fromEmail);

                MimeMessage message = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

                helper.setFrom(fromEmail, "AFFILINK");
                helper.setTo(to);
                helper.setSubject(subject);
                helper.setText(content, true); // true = HTML

                log.debug("📨 Message créé, envoi en cours...");
                mailSender.send(message);

                log.info("✅ Email envoyé avec succès (tentative {}) vers: {}", attempt, to);
                return; // Succès, on sort de la boucle

            } catch (Exception e) {
                lastException = e;
                log.error("❌ Échec de la tentative {}/{} pour {}: {} - Type: {}",
                         attempt, maxRetries, to, e.getMessage(), e.getClass().getSimpleName());

                // Log détaillé de l'erreur
                if (e instanceof MessagingException) {
                    log.error("📧 Détails MessagingException: {}", e.toString());
                }

                if (attempt < maxRetries) {
                    try {
                        log.info("⏳ Attente de {} secondes avant la prochaine tentative...", attempt);
                        Thread.sleep(1000 * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("Interruption lors du retry", ie);
                    }
                }
            }
        }

        // Si on arrive ici, toutes les tentatives ont échoué
        log.error("💥 ÉCHEC TOTAL après {} tentatives vers {}: {}", maxRetries, to, lastException.getMessage());
        throw new Exception(String.format("Échec de l'envoi après %d tentatives vers %s: %s",
                                         maxRetries, to, lastException.getMessage()), lastException);
    }
    
    /**
     * Génère le contenu HTML pour l'email de validation de commande
     */
    private String generateOrderValidationContent(User marketer, Long orderId, String orderDetails) {
        return templateService.generateOrderValidationEmail(marketer.getFirstName(), orderId, orderDetails);
    }

    /**
     * Génère le contenu HTML pour l'email de rejet de commande
     */
    private String generateOrderRejectionContent(User marketer, Long orderId) {
        String defaultReason = "Votre commande ne respecte pas nos critères de validation. Veuillez vérifier les informations et resoumettez une nouvelle commande.";
        return templateService.generateOrderRejectionEmail(marketer.getFirstName(), orderId, defaultReason);
    }
}
