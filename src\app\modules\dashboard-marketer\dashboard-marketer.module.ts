import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBadgeModule } from '@angular/material/badge';

// Bootstrap
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

// ApexCharts
import { NgApexchartsModule } from 'ng-apexcharts';

// Routing
import { DashboardMarketerRoutingModule } from './dashboard-marketer-routing.module';

// Components
import { DashboardMarketerComponent } from './dashboard-marketer.component';

// Services
import { DashboardMarketerService } from './dashboard-marketer.service';

@NgModule({
  declarations: [
    DashboardMarketerComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    
    // Angular Material
    MatCardModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatBadgeModule,
    
    // Bootstrap
    NgbModule,

    // ApexCharts
    NgApexchartsModule,

    // Routing
    DashboardMarketerRoutingModule
  ],
  providers: [
    DashboardMarketerService
  ]
})
export class DashboardMarketerModule { }
